# 大宗临购浮动价格对账单税率调整逻辑修改

## 修改文件
`src/pages/supplierSys/sheet/blockLinCai/twoSheet/twoSupplier/floatDetail.vue`

## 修改需求
1. **调整税率时，含税总金额不变**
2. **调整税率时，对账单明细的含税单价和含税金额不变**
3. **逻辑参考对账-新增二级大宗临购固定价格对账单页面**

## 修改内容

### 税率监听器逻辑修改

**修改前：**
```javascript
'reconciliationForm.taxRate': {
    handler (value) {
        let prams = twoReconciliationFixAmountM(this.tableData, value)
        this.tableData = prams.tableData
        this.reconciliationForm.taxAmount = prams.taxAmount
        this.reconciliationForm.rateAmount = prams.reconciliationAmount
        this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
    },
}
```

**修改后：**
```javascript
'reconciliationForm.taxRate': {
    handler (value) {
        let taxAmount = 0
        let rateAmount = 0 // 含税总金额保持不变
        let noRateAmount = 0
        this.tableData.forEach(item => {
            // 含税单价和含税金额保持不变，不重新计算
            // 只重新计算不含税单价、不含税金额和税额
            
            // 使用现有的含税单价和含税金额，重新计算其他字段
            const currentTotalAmount = item.totalAmount || 0 // 含税金额保持不变
            const currentPrice = item.price || 0 // 含税单价保持不变
            
            // 重新计算不含税单价：不含税单价 = 含税单价 / (1 + 税率/100)
            if (value && value > 0) {
                item.noRatePrice = this.fixed2(currentPrice / (1 + value / 100))
                // 重新计算不含税金额：不含税金额 = 含税金额 / (1 + 税率/100)
                item.noRateAmount = this.fixed2(currentTotalAmount / (1 + value / 100))
                // 重新计算税额：税额 = 含税金额 - 不含税金额
                item.taxAmount = this.fixed2(currentTotalAmount - item.noRateAmount)
            } else {
                item.noRatePrice = this.fixed2(currentPrice)
                item.noRateAmount = this.fixed2(currentTotalAmount)
                item.taxAmount = this.fixed2(0)
            }
            
            taxAmount = this.fixed2(Number(taxAmount) + Number(item.taxAmount))
            rateAmount = this.fixed2(Number(rateAmount) + Number(currentTotalAmount)) // 使用原有含税金额
            noRateAmount = this.fixed2(Number(noRateAmount) + Number(item.noRateAmount))
        })
        this.reconciliationForm.taxAmount = taxAmount
        // 含税总金额保持不变，使用原有值
        // this.reconciliationForm.rateAmount = rateAmount // 注释掉，保持原值不变
        this.reconciliationForm.noRateAmount = noRateAmount
    },
}
```

## 核心逻辑说明

### 1. 保持不变的字段
- **含税总金额** (`reconciliationForm.rateAmount`)：不重新赋值，保持原有值
- **含税单价** (`item.price`)：使用现有值，不重新计算
- **含税金额** (`item.totalAmount`)：使用现有值，不重新计算

### 2. 重新计算的字段
- **不含税单价** (`item.noRatePrice`)：`含税单价 / (1 + 税率/100)`
- **不含税金额** (`item.noRateAmount`)：`含税金额 / (1 + 税率/100)`
- **税额** (`item.taxAmount`)：`含税金额 - 不含税金额`
- **不含税总金额** (`reconciliationForm.noRateAmount`)：所有明细不含税金额的累加
- **总税额** (`reconciliationForm.taxAmount`)：所有明细税额的累加

### 3. 计算公式
```javascript
// 不含税单价计算
item.noRatePrice = this.fixed2(currentPrice / (1 + value / 100))

// 不含税金额计算
item.noRateAmount = this.fixed2(currentTotalAmount / (1 + value / 100))

// 税额计算
item.taxAmount = this.fixed2(currentTotalAmount - item.noRateAmount)
```

### 4. 边界处理
- 当税率为0或无效值时，不含税单价等于含税单价，不含税金额等于含税金额，税额为0
- 所有金额计算都使用 `fixed2` 方法保持2位小数精度

## 代码清理
- 移除了未使用的 `twoReconciliationFixAmountM` 导入
- 添加了详细的注释说明

## 测试要点
1. 调整税率时，含税总金额应保持不变
2. 调整税率时，明细中的含税单价和含税金额应保持不变
3. 不含税单价、不含税金额、税额应根据新税率重新计算
4. 不含税总金额和总税额应正确汇总
5. 所有金额应保持2位小数精度

## 参考页面
修改逻辑参考了对账-新增二级大宗临购固定价格对账单页面的税率处理方式，确保在税率调整时保持含税金额不变的业务逻辑。
