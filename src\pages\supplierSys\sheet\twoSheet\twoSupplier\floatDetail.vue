<template>
    <div class="base-page">
        <div class="e-form">
            <!--            浮动-->
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" style="padding-top: 70px;" v-loading="formLoading">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="对账单详情" name="baseInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="对账单明细" name="reconciliationDtl" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="baseInfo" class="con">
                            <div class="tabs-title" id="baseInfo">对账单详情</div>
                            <el-form
                                :model="reconciliationForm" :rules="reconciliationFormRules" label-width="200px"
                                ref="reconciliationFormRef" :disabled="false" class="demo-ruleForm"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账时间：" prop="startEndTme">
                                            <el-date-picker
                                                value-format="yyyy-MM-dd HH:mm:ss"
                                                v-model="reconciliationForm.startEndTme"
                                                type="daterange"
                                                :clearable="false"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                            >
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-if="reconciliationForm.startEndTme[1]!=null">
                                        <el-form-item label="自营店名称：" prop="supplierName">
                                            <el-input
                                                placeholder="请选择自营店名称" clearable disabled
                                                v-model="reconciliationForm.supplierName"
                                            ></el-input>
                                            <el-button
                                                size="mini"
                                                type="primary"
                                                @click="selectContractClick"
                                            >选择
                                            </el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-col :span="12" >
                                            <el-form-item label="选择物资：" prop="">
                                                <el-button size="mini"
                                                           :disabled="(reconciliationForm.startEndTme == null ||  reconciliationForm.startEndTme.length == 0)&&reconciliationForm.twoSupplierName!=''"
                                                           type="primary"
                                                           @click="selectMaterialBtnClick">选择物资
                                                </el-button>
                                            </el-form-item>
                                        </el-col>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="税率：" prop="taxRate">
                                            <el-input-number
                                                :precision="2" :max=100 style="width: 120px;min-width: 100px;" clearable
                                                v-model="reconciliationForm.taxRate"
                                            ></el-input-number>
                                            %
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：" prop="rateAmount">
                                            <el-input
                                                type="number" style="width: 200px;min-width: 150px;" clearable disabled
                                                v-model="reconciliationForm.rateAmount"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="不含税总金额：" prop="noRateAmount">
                                            <el-input-number
                                                style="width: 200px;min-width: 150px;" clearable disabled
                                                v-model="reconciliationForm.noRateAmount"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="税额：" prop="taxAmount">
                                    <span>{{reconciliationForm.taxAmount}}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注：" prop="remarks">
                                            <el-input
                                                style="width: 1000px;" type="textarea" :auto-resize="false"
                                                v-model="reconciliationForm.remarks"
                                                placeholder="请输入备注" maxlength="1000"
                                                show-word-limit
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <!--计划清单-->
                        <div id="reconciliationDtl" class="con">
                            <div class="tabs-title" id="reconciliationDtl">对账单明细</div>
                            <div class="e-table" style="background-color: #fff">
                                <el-table
                                    ref="tableRef"
                                    border
                                    style="width: 100%"
                                    :data="tableData"
                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                                    :row-style="{ fontSize: '14px', height: '48px' }"
                                >
                                    <el-table-column
                                        label="序号" type="index" width="60"
                                        fixed="left"
                                    ></el-table-column>
                                    <el-table-column label="单据日期" prop="receivingDate" width="160">
                                        <template v-slot="scope">
                                            <span>{{ scope.row.receivingDate.split(' ')[0] }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="类型" width="120" fixed="left">
                                        <template v-slot="scope">
                                            <el-tag type="danger" v-show="scope.row.reconciliationType===2">退货对账</el-tag>
                                            <el-tag type="success" v-show="scope.row.reconciliationType===1">发货对账</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="orderSn" label="订单号" width="220"/>
                                    <el-table-column prop="materialName" label="物资名称" width="200"/>
                                    <el-table-column prop="productName" label="商品名称" width="200"/>
                                    <el-table-column prop="spec" label="规格型号" width=""/>
                                    <el-table-column prop="unit" label="单位" width=""/>
                                    <el-table-column prop="quantity" label="对账数量" width=""/>
                                    <el-table-column prop="price" label="含税单价" width=""/>
                                    <el-table-column prop="noRatePrice" label="不含税单价" width=""/>
                                    <el-table-column prop="totalAmount" label="含税金额" width="">
                                    </el-table-column>
                                    <el-table-column prop="noRateAmount" label="不含税金额" width=""/>
                                  <el-table-column prop="taxAmount" label="税额" width="120px"/>
                                    <!--                                    <el-table-column prop="remarks" label="备注" width="" >-->
                                    <!--                                        <template v-slot="scope">-->
                                    <!--                                            <el-input-->
                                    <!--                                                v-model="scope.row.remarks"-->
                                    <!--                                                @change="getChangedRow(scope.row)">-->
                                    <!--                                            </el-input>-->
                                    <!--                                        </template>-->
                                    <!--                                    </el-table-column>-->
                                </el-table>
                            </div>
                        </div>
                    </div>
                </el-tabs>
            </div>
            <div class="buttons">
                <el-button type="primary" @click="saveSheetM">保存</el-button>
                <el-button type="primary" class="btn-greenYellow" @click="saveAndSubmitSheetM">保存并提交</el-button>
                <el-button @click="handleClose">返回</el-button>
            </div>
            <el-dialog
                v-dialogDrag :title="selectTwoSupplierTableTitle" :visible.sync="showTwoSupplier" width="80%"
                style="margin-left: 10%;" :close-on-click-modal="false"
            >
                <div class="e-table" style="background-color: #fff" v-loading="selectTwoSupplierLoading">
                    <div class="top dfa" style="height: 50px; padding-left: 10px">
                        <el-input
                            style="width: 200px; " type="text" @blur="getTwoSupplierListM"
                            placeholder="输入搜索关键字" v-model="keywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getTwoSupplierListM"/>
                        </el-input>
                    </div>
                    <el-table
                        ref="selectTwoSupplierR"
                        border
                        style="width: 100%"
                        @selection-change="selectTwoSupplierSelectM"
                        :data="selectSupplierDate"
                        class="table"
                        :max-height="$store.state.tableHeight"
                    >
                        <!--                        <el-table-column type="selection" width="40"></el-table-column>-->
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="" label="操作" width="">
                            <template v-slot="scope">
                                <div
                                    class="pointer" style="color: rgba(33, 110, 198, 1);"
                                    @click="supplierRowClick(scope.row)"
                                >选择自营店
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="自营店名称" prop="enterpriseName" width=""></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <span slot="footer">
                    <Pagination
                        :total="paginationInfo.total"
                        :pageSize.sync="paginationInfo.pageSize"
                        :currentPage.sync="paginationInfo.currentPage"
                        @currentChange="currentChangeUser"
                        @sizeChange="sizeChangeUser"
                    />
                </span>
            </el-dialog>
            <el-dialog
                v-dialogDrag custom-class="dlg" width="90%" title="物资选择"
                :visible.sync="selectMaterialShow"
            >
                <div class="box-left">
                    <div class="e-table">
                        <div class="top">
                            <div style="width: 200px">
                                <el-input type="text" @blur="getTwoMaterialOrderListM" placeholder="输入搜索关键字"
                                          v-model="keywords2">
                                    <img :src="require('@/assets/search.png')" slot="suffix"
                                         @click="getTwoMaterialOrderListM"/>
                                </el-input>
                            </div>
                        </div>
                        <el-table ref="bidingOrderItemRef"
                                  border
                                  max-height="340px"
                                  @selection-change="selectContractOrPlanSelectM"
                                  @row-click="selectContractOrPlanClickM"
                                  :data="contractOrderTableDate"
                                  v-loading="selectContractOrderLoading"
                                  class="table"
                        >
                            <el-table-column type="selection" width="40"></el-table-column>
                            <el-table-column label="序号" type="index" width="60"></el-table-column>
                            <el-table-column prop="supplierName" label="供应商名称" width=""></el-table-column>
                            <el-table-column prop="orderSn" label="订单号" width=""></el-table-column>
                            <el-table-column prop="gmtCreate" label="创建日期" width="">
                                <template v-slot="scope">
                                    {{ scope.row.gmtCreate }}
                                </template>
                            </el-table-column>
                        </el-table>
                        <Pagination
                            v-if="contractOrderTableDate != null && contractOrderTableDate.length > 0"
                            :total="paginationInfo1.total"
                            :pageSize.sync="paginationInfo1.pageSize"
                            :currentPage.sync="paginationInfo1.currentPage"
                            @currentChange="currentChangeUser1"
                            @sizeChange="sizeChangeUser1"
                        />
                    </div>
                </div>
                <div class="box-right">
                    <div class="e-table">
                        <div class="top">
                            <div style="width: 200px">
                                <el-input type="text" @blur="getTwoMaterialOrderListM" placeholder="输入搜索关键字"
                                          v-model="keywords3">
                                    <img :src="require('@/assets/search.png')" slot="suffix"
                                         @click="getTwoMaterialOrderListM"/>
                                </el-input>
                            </div>
                        </div>
                        <el-table ref="siteReceivingTableRef"
                                  v-loading="siteReceivingLoading"
                                  border
                                  max-height="390px"
                                  @selection-change="siteReceivingTableSelectM"
                                  @row-click="siteReceivingTableRowClickM"
                                  :data="reconciliationFormDate"
                                  class="table"
                        >
                            <el-table-column label="序号" type="index" width="60" fixed="left"/>
                            <el-table-column label="单据日期" prop="receivingDate" width="160">
                                <template v-slot="scope">
                                    <span>{{ scope.row.receivingDate.split(' ')[0] }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="类型" width="120" fixed="left">
                                <template v-slot="scope">
                                    <el-tag type="danger" v-show="scope.row.reconciliationType===2">退货对账
                                    </el-tag>
                                    <el-tag type="success" v-show="scope.row.reconciliationType===1">发货对账
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="orderSn" label="订单号" width="220"/>
                            <el-table-column prop="materialName" label="物资名称" width="200"/>
                            <el-table-column prop="productName" label="商品名称" width="200"/>
                            <el-table-column prop="spec" label="规格型号"/>
                            <el-table-column prop="unit" label="单位"/>
                            <el-table-column prop="quantity" label="对账数量"/>
                            <!--                            <el-table-column prop="price" label="含税单价"/>-->
                            <!--                            <el-table-column prop="noRatePrice" label="不含税单价"/>-->
                            <!--                            <el-table-column prop="totalAmount" label="含税金额"/>-->
                            <!--                            <el-table-column prop="noRateAmount" label="不含税金额"/>-->
                            <!--                            <el-table-column prop="totalQuantity" label="总数量" width=""></el-table-column>-->
                        </el-table>
                        <Pagination
                            v-show="siteReceivingTableDate != null && siteReceivingTableDate.length > 0"
                            :total="paginationInfo2.total"
                            :pageSize.sync="paginationInfo2.pageSize"
                            :currentPage.sync="paginationInfo2.currentPage"
                            @currentChange="currentChangeUser2"
                            @sizeChange="sizeChangeUser2"
                        />
                    </div>
                </div>
                <span slot="footer">
                <el-button type="primary" @click="sumbitOrderDate">确认选择</el-button>
                <el-button @click="selectMaterialShow = false">取消</el-button>
            </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import { calculateNotTarRateAmount, toFixed } from '@/utils/common'
import '@/utils/jquery.scrollTo.min'
import { getUuid, throttle } from '@/utils/common'
import {
    ReconciliationSupplierCreate,
    getReconciliationDtlList,
    getTwoSupplierMaterialOrderList
} from '@/api/reconciliation/twoReconciliation'
import { getEnterpriceList } from '@/api/shopManage/shop/shopSupplierRele'
import { getTwoSupplierInfo } from '@/api/platform/supplier/supplierAudit'

import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import { capitalsItemNum, capitalsAndNum, taxRateItemAmount } from '@/utils/material_reconciliationUtils/compute'
import { twoReconciliationAmountM } from '@/utils/material_reconciliationUtils/twoCompute'

export default {
    components: {
        Pagination
    },
    data () {
        return {
            //订单选择
            selectMaterialShow: false,
            keywords3: '',  //查询可对账物资关键字
            selectContractOrderLoading: '',
            contractOrderTableDate: [],
            reconciliationFormDate: [],
            paginationInfo1: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            productTypeDis: false,
            formLoading: false,
            reconciliationFormRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' },
                ],
                taxRate: [
                    { required: true, message: '请输入公司税率', trigger: 'blur' },
                    { validator: this.validateNumber, message: '供应商税率不能超过100', trigger: 'blur' }
                ],

            },
            siteReceivingTableSelectRowData: [],
            selectTwoSupplierRowDate: [],
            siteReceivingTableDate: [],
            siteReceivingLoading: false,
            showSiteReceivingDia: false,
            keywords2: null,
            paginationInfo2: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            selectTwoSupplierTableTitle: null,
            selectTwoSupplierLoading: false,
            showTwoSupplier: false,
            selectSupplierDate: [],
            keywords: null,
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            reconciliationForm: {
                billId: null,
                billNo: null,
                businessType: 2,
                orderId: null,
                orderSn: null,
                twoSupplierOrgId: null,
                twoSupplierName: null,
                supplierName: null,
                shopId: null,
                supplierOrgId: null,
                supplierEnterpriseId: null,
                noRateAmount: null,
                rateAmount: null,
                totalAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: 2,
                twoSupplierIsAffirm: 0,
                twoSupplierAffirmTime: null,
                supplierAffirmTime: null,
                supplierIsAffirm: null,
                taxRate: 0,
                // 内外供应商信息
                creditCode: null,
                orgShort: null,
                dtl: [],
                // TODO 待完善

            },
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            maxNum: *********,
            tableData: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
        }
    },
    created () {
        this.reconciliationForm.createType = this.$route.query.createType
        this.getTwoSupplierTaxRateM()
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal + 300)
            }
        },
        'reconciliationForm.taxRate': {
            handler (value) {
                let taxAmount = 0
                let rateAmount = 0
                let noRateAmount = 0
                this.tableData.forEach(item => {
                    let prams = taxRateItemAmount(item.price, item.quantity, value)
                    item.noRatePrice = prams.noRatePrice
                    item.taxAmount = prams.taxAmount
                    item.totalAmount = prams.acceptanceAmount
                    item.noRateAmount =  prams.acceptanceNoRateAmount
                    taxAmount = this.fixed2(Number(taxAmount) + Number(item.taxAmount))
                    rateAmount = this.fixed2(Number(rateAmount) + Number(item.totalAmount))
                    noRateAmount = this.fixed2(Number(noRateAmount) + Number(item.noRateAmount))
                })
                this.reconciliationForm.rateAmount = rateAmount
                this.reconciliationForm.taxAmount = taxAmount
                this.reconciliationForm.noRateAmount = capitalsAndNum(rateAmount, noRateAmount,  this.reconciliationForm.taxRate)
            },
        }
    },
    methods: {
        //订单确定对账明细
        sumbitOrderDate () {
            this.selectMaterialShow = false
            this.tableData = this.reconciliationFormDate

            let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = prams.tableData
            this.reconciliationForm.taxAmount = prams.taxAmount
            this.reconciliationForm.rateAmount = prams.reconciliationAmount
            this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
        },
        //打开物资选择弹窗
        selectMaterialBtnClick () {
            this.siteReceivingTableDate = []
            this.selectMaterialShow = true
            this.getTwoMaterialOrderListM()
        },
        getTwoMaterialOrderListM () {
            if (this.reconciliationForm.supplierOrgId == null || this.reconciliationForm.supplierOrgId == '') {
                this.$message.warning('请先选择自营店名称')
                this.selectMaterialShow = false
                return
            }
            let params = {
                supplierOrgId: this.reconciliationForm.supplierOrgId,
                shopId: this.reconciliationForm.shopId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                userType: 2,
                sourceType: 2,
                limit: 100,
                page: 1,
                productType: 10
            }
            if (this.keywords2 != null) {
                params.keywords = this.keywords2
            }
            if (this.reconciliationForm.type != null) {
                params.billType = this.reconciliationForm.type
            }
            this.selectContractOrderLoading = true
            getTwoSupplierMaterialOrderList(params).then(res => {
                this.paginationInfo1.total = res.totalCount
                this.paginationInfo1.pageSize = res.pageSize
                this.paginationInfo1.currentPage = res.currPage
                this.contractOrderTableDate = res.list
                this.selectContractOrderLoading = false
            }).finally(() => {
                this.selectContractOrderLoading = false
            })

        },
        //选择订单生成可对账物资
        selectContractOrPlanSelectM (value) {
            this.selectOrderDate = value
            if (this.selectOrderDate.length > 0) {
                this.orderIds = this.selectOrderDate.map(item => item.parentOrderId)
                this.getReconciliationDtlListM2()
            }else {
                this.reconciliationFormDate = []
            }

        },
        selectContractOrPlanClickM (selectRow) {
            selectRow.flag = !selectRow.flag
            this.$refs.bidingOrderItemRef.toggleRowSelection(selectRow, selectRow.flag)
        },
        //查询可对账物资
        getReconciliationDtlListM2 () {
            let params = {
                supplierOrgId: this.reconciliationForm.supplierOrgId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                userType: 2,
                sourceType: 2,
                productType: 10,
                limit: 100,
                page: 1,

            }
            if (this.keywords3 != null) {
                params.keywords = this.keywords3
            }
            if (this.orderIds != null && this.orderIds.length > 0) {
                params.orderIds = this.orderIds
            }
            getReconciliationDtlList(params).then(res => {
                this.reconciliationFormDate = res.list

                this.reconciliationFormDate = res.list
                let prams = twoReconciliationAmountM(this.tableData, this.reconciliationForm.taxRate)
                this.tableData = prams.tableData
                this.reconciliationForm.taxAmount = prams.taxAmount
                this.reconciliationForm.rateAmount = prams.reconciliationAmount
                this.reconciliationForm.noRateAmount = prams.reconciliationNoRateAmount
            })
        },
        validateNumber (rule, value, callback) {
            if (value < 0 || value >= 100) {
                callback(new Error('供应商税率不能超过100'))
            } else {
                callback()
            }
        },

        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        getTwoSupplierTaxRateM () {
            getTwoSupplierInfo().then(res => {
                this.reconciliationForm.twoSupplierName = res.enterpriseName
                this.reconciliationForm.twoSupplierOrgId = res.enterpriseId
                // this.reconciliationForm.taxRate = res.taxRate
            })
        },
        selectContractClick () {
            this.selectTwoSupplierTableTitle = '选择自营店'
            this.getTwoSupplierListM()
            this.showTwoSupplier = true
        },
        selectTwoSupplierSelectM (value) {
            this.selectTwoSupplierRowDate = value
        },

        supplierRowClick (row) {
            this.reconciliationForm.supplierName = row.enterpriseName
            this.reconciliationForm.shopId = row.shopId
            this.reconciliationForm.supplierOrgId = row.enterpriseId
            this.showTwoSupplier = false

        // this.getReconciliationDtlListM()
        },
        getReconciliationDtlListM () {
            let params = {
                supplierOrgId: this.reconciliationForm.supplierOrgId,
                startTime: this.reconciliationForm.startEndTme[0],
                endTime: this.reconciliationForm.startEndTme[1],
                userType: 2,
                sourceType: 2,
                limit: 100,
                page: 1,

            }
            getReconciliationDtlList(params).then(res => {
                this.tableData = res.list

                this.tableData = res.list
                let noRateAmount = 0
                let rateAmount = 0
                this.tableData.forEach(item => {
                    item.noRatePrice = calculateNotTarRateAmount(item.price, this.reconciliationForm.taxRate)
                    item.totalAmount = this.fixed2(Number(item.quantity) * Number(item.price))
                    item.noRateAmount = capitalsItemNum(calculateNotTarRateAmount(item.totalAmount, this.reconciliationForm.taxRate), item.noRatePrice, item.quantity)
                    rateAmount = this.fixed2(Number(rateAmount) + Number(item.totalAmount))
                    noRateAmount = this.fixed2(Number(noRateAmount) + Number(item.noRateAmount))
                })
                this.reconciliationForm.noRateAmount = noRateAmount
                this.reconciliationForm.rateAmount = rateAmount
            })
        },
        siteReceivingTableSelectM (value) {
            this.siteReceivingTableSelectRowData = value
        },
        siteReceivingTableRowClickM (row) {
            row.flag = !row.flag
            this.$refs.siteReceivingTableRef.toggleRowSelection(row, row.flag)
        },
        currentChangeUser2 (currPage) {
            this.paginationInfo2.currentPage = currPage
            this.getTwoMaterialOrderListM()
        },
        sizeChangeUser2 (pageSize) {
            this.paginationInfo2.pageSize = pageSize
            this.getTwoMaterialOrderListM()
        },
        currentChangeUser (currPage) {
            this.paginationInfo.currentPage = currPage
            this.getTwoSupplierListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo.pageSize = pageSize
            this.getTwoSupplierListM()
        },
        currentChangeUser1 (currPage) {
            this.paginationInfo1.currentPage = currPage
            this.getTwoMaterialOrderListM()
        },
        sizeChangeUser1 (pageSize) {
            this.paginationInfo1.pageSize = pageSize
            this.getTwoMaterialOrderListM()
        },
        // 自营店
        getTwoSupplierListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: 10,
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            this.selectTwoSupplierLoading = true
            getEnterpriceList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.selectSupplierDate = res.list

            }).finally(() => {
                this.selectTwoSupplierLoading = false
            })
        },
        saveSheetM () {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.price == 0.00) {
                    return this.$message.error('对账含税单价不能为0！')
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要保存吗！', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.state = 5
                        ReconciliationSupplierCreate(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('保存成功')
                                this.$router.go(-1)
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        saveAndSubmitSheetM () {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.price == 0.00) {
                    return this.$message.error('对账含税单价不能为0！')
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要保存并提交吗！', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.state = 1
                        this.reconciliationForm.createType = 2
                        ReconciliationSupplierCreate(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('保存成功')
                                this.$router.go(-1)
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        // 选择计划
        selectPlanClick () {
            this.selectTwoSupplierTableTitle = '选择计划'
            this.getTwoSupplierListM()
            this.showTwoSupplier = true
        },

        // 删除单
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if (t.uuid != row.uuid) {
                    return true
                } else {
                    return false
                }
            })
        },
        // 拆单
        dismantleM (row) {
        // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                acceptanceAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },

        // 表单变化
        getChangedRow (row) {
        // 处理数量
            this.disposeQuantityM(row)
            // 计算金额
            this.countAmountM()
        },
        priceChange (row) {
        // 一旦变化则是1
            row.updateType = 1
            // 处理固定费用
            this.disposeFixationPriceM(row)
            // 处理到店网价
            this.disposeFreightPriceM(row)
            // 计算金额
            this.countAmountM()
        },
        countAmountM () {
            let amount = 0
            // 最终计算
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                t.price = this.fixed2(Number(t.freightPrice) + Number(t.fixationPrice))
                t.acceptanceAmount = this.fixed2(Number(t.quantity) * Number(t.price))
                amount += Number(t.acceptanceAmount)
            }
            this.reconciliationForm.reconciliationAmount = this.fixed2(amount)
        },
        // 处理固定费用
        disposeFixationPriceM (row) {
            if (row.fixationPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.fixationPrice = this.fixed2(0)
            } else {
                row.fixationPrice = this.fixed2(row.fixationPrice)
            }
        },
        // 处理到货网价
        disposeFreightPriceM (row) {
            if (row.freightPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.freightPrice = this.fixed2(0)
            } else {
                row.freightPrice = this.fixed2(row.freightPrice)
            }
        },
        // 处理数量
        disposeQuantityM (row) {
            if (row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            let countNum = 0
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.materialName === row.materialName && t.spec === row.spec && t.unit == row.unit && t.uuid !== row.uuid) {
                    countNum++
                    if (maxNum <= 0) {
                        maxNum = 0
                        continue
                    }
                    maxNum = maxNum - t.quantity
                }
            }
            // 如果一次没添加，则表示操作的第一个
            if (countNum == 0) {
                maxNum = row.maxQuantity
            }
            if (row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            } else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
            // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        },
    }
    ,
    mounted ()
    {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliationDtl']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
        // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    }
    ,
}
</script>

<style lang='scss' scoped>
.base-page {
    flex-grow: 1;
}
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    height: unset !important;
    display: flex;
    align-items: center;
}

.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}

/deep/ .el-dialog.dlg {
    height: 600px;

    .el-dialog__header {
        margin-bottom: 0;
    }

    .el-dialog__body {
        height: 474px;
        margin: 10px;
        display: flex;

        & > div {
            .e-pagination {
                background-color: unset;
            }

            //height: 670px;
            .title {
                height: 22px;
                margin-bottom: 10px;
                padding-left: 26px;
                text-align: left;
                line-height: 22px;
                color: #2e61d7;
                font-weight: bold;
                position: relative;
                display: flex;

                &::before {
                    content: '';
                    display: block;
                    width: 10px;
                    height: inherit;
                    border-radius: 5px;
                    background-color: blue;
                    position: absolute;
                    left: 10px;
                    top: 0;
                }
            }
        }

        .el-input__inner {
            border: 1px solid blue;
            border-radius: 6px;
        }

        .el-input__suffix {
            width: 20px;
        }

        .e-table {
            flex-grow: 1;

            .table {
                height: 100%;
            }
        }

        .box-left {
            width: 660px;
            display: flex;
            flex-direction: column;

            .top {
                box-shadow: unset;
            }
        }

        .box-right {
            flex-grow: 1;
            display: flex;
            flex-direction: column;

            & > div {
                display: flex;
                flex-direction: column;
            }

            .top {
                justify-content: left;
                border-radius: 0;
                box-shadow: unset;
            }

            .bottom {
                flex-grow: 1;
            }
        }
    }

    .el-dialog__footer {
        background-color: #eff2f6;
    }
}
</style>