# 物资选择左边列表接口新增总结

## 修改内容

### API接口新增
**文件：** `src/api/reconciliation/twoReconciliation.js`

**新增接口方法：**
```javascript
//物资公司二级订单查询订单信息（新接口）
const getHaveTwoMaterialOrderList = params => {
    return httpPost({
        url: '/materialMall/orders/getHaveTwoMaterialOrderList',
        params
    })
}
```

**原有接口保持不变：**
```javascript
//物资公司二级订单查询订单信息
const getTwoMaterialOrderList = params => {
    return httpPost({
        url: '/materialMall/orders/getTwoMaterialOrderList',
        params
    })
}
```

### 导出列表更新
在文件末尾的导出列表中添加了新方法：
```javascript
export {
    ReconciliationSupplierCreate,
    getReconciliationDtlList,
    getTwoMaterialOrderList,        // 原有方法
    getHaveTwoMaterialOrderList,    // 新增方法
    getTwoSupplierMaterialOrderList,
    deleteById,
    supplierListByEntity,
    getTwoSupplierList,
    updateState,
    materialReconciliationSubmit,
    outputExcel,
    update,
    supplierReconciliationSupplierAffirm,
    twoSupplierReconciliationSupplierAffirm,
    findByNo,
    saveAndUpdate
}
```

## 接口对比

### 原有接口
- **方法名：** `getTwoMaterialOrderList`
- **请求地址：** `/materialMall/orders/getTwoMaterialOrderList`
- **状态：** 保持不变，继续可用

### 新增接口
- **方法名：** `getHaveTwoMaterialOrderList`
- **请求地址：** `/materialMall/orders/getHaveTwoMaterialOrderList`
- **状态：** 新增，可供使用

## 使用方式

### 在页面中导入
```javascript
import { 
    getTwoMaterialOrderList,        // 原有方法
    getHaveTwoMaterialOrderList     // 新增方法
} from '@/api/reconciliation/twoReconciliation'
```

### 调用方式
```javascript
// 使用原有接口
getTwoMaterialOrderList(params).then(res => {
    // 处理响应
})

// 使用新增接口
getHaveTwoMaterialOrderList(params).then(res => {
    // 处理响应
})
```

## 接口参数
两个接口的参数格式完全相同，包括但不限于：
- `page`: 分页页码
- `limit`: 分页大小
- `supplierId` / `twoSupplierOrgId`: 供应商ID
- `startTime`: 开始时间
- `endTime`: 结束时间
- `productType`: 产品类型
- `keywords`: 搜索关键字
- `billType`: 单据类型
- `userType`: 用户类型
- `sourceType`: 来源类型

## 应用场景
新增的 `getHaveTwoMaterialOrderList` 接口可以用于：
- 物资选择弹框左边列表的订单查询
- 各种对账单页面的物资选择功能
- 需要特定数据筛选逻辑的场景

## 完成状态
✅ 新增接口方法已定义完成
✅ 导出列表已更新
✅ 原有接口保持不变
✅ 两个接口可以并存使用

## 后续使用
现在开发人员可以根据业务需求选择使用：
- `getTwoMaterialOrderList` - 原有接口逻辑
- `getHaveTwoMaterialOrderList` - 新接口逻辑

具体使用哪个接口需要根据后端接口的业务逻辑差异来决定。
